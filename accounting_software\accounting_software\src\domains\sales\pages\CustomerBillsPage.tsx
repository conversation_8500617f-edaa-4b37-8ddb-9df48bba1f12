import React, { useState, useEffect, useCallback } from 'react';
import {
  <PERSON>,
  <PERSON>po<PERSON>,
  <PERSON>ton,
  Card,
  CardContent,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
  IconButton,
  Tooltip,
  TextField,
  InputAdornment,
  TablePagination,
  CircularProgress,
  Alert,
  Dialog,
  Menu,
  MenuItem,
  ListItemIcon,
  ListItemText,
} from '@mui/material';
import {
  Add as AddIcon,
  Search as SearchIcon,
  Visibility as VisibilityIcon,
  Edit as EditIcon,
  Receipt as ReceiptIcon,
  MoreVert as MoreVertIcon,
  Assignment as AssignmentIcon,
} from '@mui/icons-material';
import { useNavigate, useLocation } from 'react-router-dom';
import { formatCurrency } from '../../../shared/utils/formatters';
import { useCurrencyInfo } from '../../gl/hooks/useCurrencyInfo';
import { customerBillService, type CustomerBill } from '../../../services/customer-bill.service';
import CreateCustomerBillPage from './CreateCustomerBillPage.tsx';

const CustomerBillsPage: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { currencyInfo } = useCurrencyInfo();
  const [customerBills, setCustomerBills] = useState<CustomerBill[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [createBillOpen, setCreateBillOpen] = useState(false);
  const [selectedBill, setSelectedBill] = useState<CustomerBill | null>(null);
  const [viewMode, setViewMode] = useState(false);
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [selectedBillForMenu, setSelectedBillForMenu] = useState<CustomerBill | null>(null);

  // Mock data for development
  const mockCustomerBills: CustomerBill[] = [
    {
      id: 1,
      bill_number: 'INV-000001',
      customer_id: 1,
      customer_name: 'ABC Corporation',
      receivable_account_id: 1,
      bill_date: '2024-03-15',
      due_date: '2024-04-14',
      reference_number: 'SO-000001',
      status: 'posted',
      payment_terms_id: 1,
      payment_terms_name: 'Net 30',
      notes: 'Invoice for laptop computers',
      line_items: [],
      subtotal: 2000.00,
      tax_amount: 200.00,
      total_amount: 2200.00,
      amount_paid: 0.00,
      balance_due: 2200.00,
      source_type: 'sales_order',
      source_document_id: 1,
      created_at: '2024-03-15T10:00:00Z',
    },
    {
      id: 2,
      bill_number: 'INV-000002',
      customer_id: 2,
      customer_name: 'XYZ Industries',
      receivable_account_id: 1,
      bill_date: '2024-03-12',
      due_date: '2024-04-11',
      reference_number: 'SO-000002',
      status: 'draft',
      payment_terms_id: 2,
      payment_terms_name: 'Net 15',
      notes: 'Invoice for office chairs',
      line_items: [],
      subtotal: 4000.00,
      tax_amount: 400.00,
      total_amount: 4400.00,
      amount_paid: 0.00,
      balance_due: 4400.00,
      source_type: 'sales_order',
      source_document_id: 2,
      created_at: '2024-03-12T14:30:00Z',
    },
  ];

  const loadCustomerBills = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      // Load saved bills from localStorage
      const savedBills = localStorage.getItem('customerBills');
      const createdBills = savedBills ? JSON.parse(savedBills) : [];

      // Combine mock data with created bills
      const allBills = [...mockCustomerBills, ...createdBills];

      // Sort by creation date (newest first)
      allBills.sort((a, b) => new Date(b.created_at || b.bill_date).getTime() - new Date(a.created_at || a.bill_date).getTime());

      setCustomerBills(allBills);
      console.log('Customer bills loaded:', allBills.length);

    } catch (err) {
      console.error('Error loading customer bills:', err);
      setError('Failed to load customer bills');
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    loadCustomerBills();
  }, [loadCustomerBills]);

  // Check if we're coming from sales order
  useEffect(() => {
    const state = location.state as any;
    if (state?.createFromSalesOrder && state?.salesOrder) {
      console.log('Creating customer bill from sales order:', state.salesOrder);
      setSelectedBill(null);
      setViewMode(false);
      setCreateBillOpen(true);
    }
  }, [location.state]);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'draft':
        return 'warning';
      case 'posted':
        return 'success';
      default:
        return 'default';
    }
  };

  const getStatusLabel = (status: string) => {
    switch (status) {
      case 'draft':
        return 'Draft';
      case 'posted':
        return 'Posted';
      default:
        return status;
    }
  };

  const filteredBills = customerBills.filter(bill =>
    bill.bill_number.toLowerCase().includes(searchTerm.toLowerCase()) ||
    bill.customer_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    bill.reference_number.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const paginatedBills = filteredBills.slice(
    page * rowsPerPage,
    page * rowsPerPage + rowsPerPage
  );

  const handleCreateBill = () => {
    setSelectedBill(null);
    setViewMode(false);
    setCreateBillOpen(true);
  };

  const handleViewBill = (bill: CustomerBill) => {
    setSelectedBill(bill);
    setViewMode(true);
    setCreateBillOpen(true);
  };

  const handleEditBill = (bill: CustomerBill) => {
    setSelectedBill(bill);
    setViewMode(false);
    setCreateBillOpen(true);
  };

  const handleMenuClick = (event: React.MouseEvent<HTMLElement>, bill: CustomerBill) => {
    setAnchorEl(event.currentTarget);
    setSelectedBillForMenu(bill);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
    setSelectedBillForMenu(null);
  };

  const handleCreateFromSalesOrder = () => {
    // Navigate to sales orders page to select one
    navigate('/dashboard/sales/orders');
    handleMenuClose();
  };

  const handlePostBill = async (bill: CustomerBill) => {
    try {
      setLoading(true);
      // Call the post API endpoint
      await customerBillService.postCustomerBill(bill.id!);
      setSuccess('Customer bill posted successfully! Journal entries have been created.');
      loadCustomerBills(); // Refresh the list
    } catch (error) {
      console.error('Error posting customer bill:', error);
      setError('Failed to post customer bill');
    } finally {
      setLoading(false);
    }
    handleMenuClose();
  };

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box p={3}>
      {/* Header */}
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Box>
          <Typography variant="h4" component="h1" gutterBottom>
            Customer Bills & Invoices
          </Typography>
          <Typography variant="body2" color="text.secondary">
            Manage customer invoices and billing
          </Typography>
        </Box>
        <Box display="flex" gap={1}>
          <Button
            variant="outlined"
            startIcon={<AssignmentIcon />}
            onClick={handleCreateFromSalesOrder}
          >
            From Sales Order
          </Button>
          <Button
            variant="contained"
            startIcon={<AddIcon />}
            onClick={handleCreateBill}
          >
            Create Invoice
          </Button>
        </Box>
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      {success && (
        <Alert severity="success" sx={{ mb: 3 }} onClose={() => setSuccess(null)}>
          {success}
        </Alert>
      )}

      {/* Search and Filters */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Box display="flex" gap={2} alignItems="center">
            <TextField
              placeholder="Search by invoice number, customer, or reference..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <SearchIcon />
                  </InputAdornment>
                ),
              }}
              sx={{ flexGrow: 1 }}
            />
          </Box>
        </CardContent>
      </Card>

      {/* Customer Bills Table */}
      <Card>
        <TableContainer>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>Invoice #</TableCell>
                <TableCell>Customer</TableCell>
                <TableCell>Date</TableCell>
                <TableCell>Due Date</TableCell>
                <TableCell>Reference</TableCell>
                <TableCell>Status</TableCell>
                <TableCell align="right">Amount</TableCell>
                <TableCell align="right">Balance Due</TableCell>
                <TableCell align="center">Actions</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {paginatedBills.map((bill) => (
                <TableRow key={bill.id} hover>
                  <TableCell>
                    <Typography variant="body2" fontWeight="medium">
                      {bill.bill_number}
                    </Typography>
                  </TableCell>
                  <TableCell>
                    <Typography variant="body2">
                      {bill.customer_name}
                    </Typography>
                  </TableCell>
                  <TableCell>
                    <Typography variant="body2">
                      {new Date(bill.bill_date).toLocaleDateString()}
                    </Typography>
                  </TableCell>
                  <TableCell>
                    <Typography variant="body2">
                      {new Date(bill.due_date).toLocaleDateString()}
                    </Typography>
                  </TableCell>
                  <TableCell>
                    <Typography variant="body2">
                      {bill.reference_number}
                    </Typography>
                  </TableCell>
                  <TableCell>
                    <Chip
                      label={getStatusLabel(bill.status)}
                      color={getStatusColor(bill.status) as any}
                      size="small"
                    />
                  </TableCell>
                  <TableCell align="right">
                    <Typography variant="body2" fontWeight="medium">
                      {formatCurrency(bill.total_amount, currencyInfo?.functional_currency_symbol)}
                    </Typography>
                  </TableCell>
                  <TableCell align="right">
                    <Typography variant="body2" fontWeight="medium">
                      {formatCurrency(bill.balance_due, currencyInfo?.functional_currency_symbol)}
                    </Typography>
                  </TableCell>
                  <TableCell align="center">
                    <Box display="flex" gap={0.5}>
                      <Tooltip title="View">
                        <IconButton size="small" onClick={() => handleViewBill(bill)}>
                          <VisibilityIcon fontSize="small" />
                        </IconButton>
                      </Tooltip>
                      <Tooltip title="Edit">
                        <IconButton
                          size="small"
                          onClick={() => handleEditBill(bill)}
                          disabled={bill.status === 'posted'}
                        >
                          <EditIcon fontSize="small" />
                        </IconButton>
                      </Tooltip>
                      <Tooltip title="More actions">
                        <IconButton
                          size="small"
                          onClick={(e) => handleMenuClick(e, bill)}
                        >
                          <MoreVertIcon fontSize="small" />
                        </IconButton>
                      </Tooltip>
                    </Box>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>

        <TablePagination
          component="div"
          count={filteredBills.length}
          page={page}
          onPageChange={(_, newPage) => setPage(newPage)}
          rowsPerPage={rowsPerPage}
          onRowsPerPageChange={(e) => {
            setRowsPerPage(parseInt(e.target.value, 10));
            setPage(0);
          }}
          rowsPerPageOptions={[5, 10, 25, 50]}
        />
      </Card>

      {/* Actions Menu */}
      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleMenuClose}
      >
        {selectedBillForMenu?.status === 'draft' && (
          <MenuItem onClick={() => {
            if (selectedBillForMenu) {
              handlePostBill(selectedBillForMenu);
            }
          }}>
            <ListItemIcon>
              <AssignmentIcon fontSize="small" />
            </ListItemIcon>
            <ListItemText>Post Bill</ListItemText>
          </MenuItem>
        )}
        <MenuItem onClick={() => {
          if (selectedBillForMenu) {
            console.log('Print invoice:', selectedBillForMenu.bill_number);
          }
          handleMenuClose();
        }}>
          <ListItemIcon>
            <ReceiptIcon fontSize="small" />
          </ListItemIcon>
          <ListItemText>Print Invoice</ListItemText>
        </MenuItem>
      </Menu>

      {/* Create/Edit Customer Bill Dialog */}
      <Dialog
        open={createBillOpen}
        onClose={() => {
          setCreateBillOpen(false);
          setSelectedBill(null);
          setViewMode(false);
        }}
        fullScreen
      >
        <CreateCustomerBillPage
          customerBill={selectedBill}
          viewMode={viewMode}
          salesOrderId={(location.state as any)?.salesOrderId}
          salesOrder={(location.state as any)?.salesOrder}
          onClose={() => {
            setCreateBillOpen(false);
            setSelectedBill(null);
            setViewMode(false);
            // Clear location state
            navigate('/dashboard/sales/customer-bills', { replace: true });
            loadCustomerBills(); // Refresh the list
          }}
        />
      </Dialog>
    </Box>
  );
};

export default CustomerBillsPage;
